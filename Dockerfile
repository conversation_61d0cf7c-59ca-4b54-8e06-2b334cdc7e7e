# TRELLIS Docker Environment
# Base image with CUDA support
FROM nvidia/cuda:11.8.0-cudnn8-devel-ubuntu20.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV CUDA_HOME=/usr/local/cuda
ENV PATH=${CUDA_HOME}/bin:${PATH}
ENV LD_LIBRARY_PATH=${CUDA_HOME}/lib64:${LD_LIBRARY_PATH}
# Set CUDA architectures for compilation (RTX 4090 is Ada Lovelace, compute capability 8.9)
ENV TORCH_CUDA_ARCH_LIST="6.0;6.1;7.0;7.5;8.0;8.6;8.9"
ENV CUDA_ARCHITECTURES="60;61;70;75;80;86;89"

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    curl \
    git \
    build-essential \
    cmake \
    ninja-build \
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    libavcodec-dev \
    libavformat-dev \
    libswscale-dev \
    libv4l-dev \
    libxvidcore-dev \
    libx264-dev \
    libgtk-3-dev \
    libatlas-base-dev \
    gfortran \
    python3 \
    python3-pip \
    python3-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Miniconda
RUN wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O /tmp/miniconda.sh && \
    bash /tmp/miniconda.sh -b -p /opt/conda && \
    rm /tmp/miniconda.sh

# Add conda to PATH
ENV PATH=/opt/conda/bin:$PATH

# Create conda environment
RUN conda create -n trellis python=3.10 -y

# Activate conda environment
SHELL ["conda", "run", "-n", "trellis", "/bin/bash", "-c"]

# Install PyTorch with CUDA 11.8
RUN conda install pytorch==2.4.0 torchvision==0.19.0 pytorch-cuda=11.8 -c pytorch -c nvidia -y

# Set working directory
WORKDIR /app

# Copy project files
COPY . /app/

# Install basic Python dependencies
RUN pip install pillow imageio imageio-ffmpeg tqdm easydict opencv-python-headless \
    scipy ninja rembg onnxruntime trimesh open3d xatlas pyvista pymeshfix igraph transformers

# Install utils3d from git
RUN pip install git+https://github.com/EasternJournalist/utils3d.git@9a4eb15e4021b67b12c460c7057d642626897ec8

# Install xformers for PyTorch 2.4.0 + CUDA 11.8
RUN pip install xformers==0.0.27.post2 --index-url https://download.pytorch.org/whl/cu118

# Try to install flash-attn from precompiled wheel, fallback to skip if fails
RUN pip install flash-attn==2.5.9.post1 --no-build-isolation --find-links https://github.com/Dao-AILab/flash-attention/releases || \
    echo "Flash-attn installation failed, will use xformers backend instead"

# Install spconv
RUN pip install spconv-cu118

# Install kaolin
RUN pip install kaolin -f https://nvidia-kaolin.s3.us-east-2.amazonaws.com/torch-2.4.0_cu121.html

# Create temporary directory for extensions
RUN mkdir -p /tmp/extensions

# Install nvdiffrast with proper CUDA arch settings
RUN git clone https://github.com/NVlabs/nvdiffrast.git /tmp/extensions/nvdiffrast && \
    cd /tmp/extensions/nvdiffrast && \
    TORCH_CUDA_ARCH_LIST="6.0;6.1;7.0;7.5;8.0;8.6" pip install .

# Install diffoctreerast
RUN git clone --recurse-submodules https://github.com/JeffreyXiang/diffoctreerast.git /tmp/extensions/diffoctreerast && \
    pip install /tmp/extensions/diffoctreerast

# Install mip-splatting
RUN git clone https://github.com/autonomousvision/mip-splatting.git /tmp/extensions/mip-splatting && \
    pip install /tmp/extensions/mip-splatting/submodules/diff-gaussian-rasterization/

# Install vox2seq
RUN cp -r extensions/vox2seq /tmp/extensions/vox2seq && \
    pip install /tmp/extensions/vox2seq

# Install demo dependencies
RUN pip install gradio==4.44.1 gradio_litmodel3d==0.0.1

# Clean up temporary files
RUN rm -rf /tmp/extensions

# Set environment variables for TRELLIS
# Use xformers as default backend (more stable than flash-attn)
ENV ATTN_BACKEND=xformers
ENV SPCONV_ALGO=native

# Create directories for temporary files and outputs
RUN mkdir -p /app/tmp /app/outputs

# Expose port for Gradio
EXPOSE 7860

# Default command
CMD ["conda", "run", "-n", "trellis", "python", "app.py", "--server-name", "0.0.0.0", "--server-port", "7860"]
